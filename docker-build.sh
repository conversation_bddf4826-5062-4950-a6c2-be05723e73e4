#!/bin/bash

# Docker build script for PR Statement Generator Backend
# Supports both development and production builds

set -e

# Default values
IMAGE_NAME="pr-generator-backend"
TAG="latest"
DOCKERFILE="Dockerfile"
BUILD_TYPE="development"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --tag TAG           Docker image tag (default: latest)"
    echo "  -n, --name NAME         Docker image name (default: pr-generator-backend)"
    echo "  -p, --production        Use production Dockerfile"
    echo "  -d, --development       Use development Dockerfile (default)"
    echo "  --no-cache              Build without using cache"
    echo "  --push                  Push image to registry after build"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      # Build development image"
    echo "  $0 -p -t v1.0.0         # Build production image with tag v1.0.0"
    echo "  $0 --no-cache           # Build without cache"
}

# Parse command line arguments
NO_CACHE=""
PUSH_IMAGE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -p|--production)
            BUILD_TYPE="production"
            DOCKERFILE="Dockerfile.production"
            shift
            ;;
        -d|--development)
            BUILD_TYPE="development"
            DOCKERFILE="Dockerfile"
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Build the image
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"

print_status "Building Docker image..."
print_status "Image name: $FULL_IMAGE_NAME"
print_status "Build type: $BUILD_TYPE"
print_status "Dockerfile: $DOCKERFILE"

# Build command
BUILD_CMD="docker build $NO_CACHE -f $DOCKERFILE -t $FULL_IMAGE_NAME ."

print_status "Running: $BUILD_CMD"
echo ""

if eval $BUILD_CMD; then
    print_success "Docker image built successfully: $FULL_IMAGE_NAME"
    
    # Show image size
    IMAGE_SIZE=$(docker images --format "table {{.Size}}" $FULL_IMAGE_NAME | tail -n 1)
    print_status "Image size: $IMAGE_SIZE"
    
    # Push image if requested
    if [[ "$PUSH_IMAGE" == true ]]; then
        print_status "Pushing image to registry..."
        if docker push $FULL_IMAGE_NAME; then
            print_success "Image pushed successfully"
        else
            print_error "Failed to push image"
            exit 1
        fi
    fi
    
    echo ""
    print_success "Build completed successfully!"
    print_status "To run the container:"
    echo "  docker run -p 8000:8000 -e GROQ_API_KEY=your-api-key $FULL_IMAGE_NAME"
    echo ""
    print_status "To run with docker-compose:"
    echo "  docker-compose up"
    
else
    print_error "Docker build failed"
    exit 1
fi
