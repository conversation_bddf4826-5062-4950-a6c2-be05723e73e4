# Docker ignore file for PR Statement Generator Backend
# Excludes unnecessary files from Docker build context

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Test files
tests/
test_*.py
*_test.py
pytest.ini
.pytest_cache/
.coverage
htmlcov/

# Development files
.env.example
run_tests.py
test_api.py
test_functional.py

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
.tmp/

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Other
.editorconfig
.pre-commit-config.yaml
