version: '3.8'

services:
  pr-generator-backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - GROQ_API_KEY=${GROQ_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      # Mount logs directory for development
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pr-generator-network

networks:
  pr-generator-network:
    driver: bridge
