#!/usr/bin/env python3
"""
Test runner script for PR Statement Generator Backend.

This script provides a simple way to run tests with proper environment setup
and can be used in CI/CD pipelines.
"""

import os
import sys
import subprocess
from pathlib import Path


def setup_test_environment():
    """Set up environment variables for testing."""
    # Set test environment variables
    os.environ["GROQ_API_KEY"] = "test-api-key"
    os.environ["LOG_LEVEL"] = "ERROR"
    
    # Ensure we're in the correct directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("✅ Test environment configured")


def run_tests(test_args=None):
    """Run the test suite using pytest."""
    if test_args is None:
        test_args = []
    
    # Base pytest command
    cmd = [sys.executable, "-m", "pytest"]
    
    # Add default arguments if none provided
    if not test_args:
        cmd.extend([
            "tests/",
            "--verbose",
            "--tb=short",
            "--color=yes"
        ])
    else:
        cmd.extend(test_args)
    
    print(f"🧪 Running tests with command: {' '.join(cmd)}")
    print("=" * 60)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1


def main():
    """Main test runner function."""
    print("🚀 PR Statement Generator Backend - Test Runner")
    print("=" * 60)
    
    # Setup environment
    setup_test_environment()
    
    # Parse command line arguments
    test_args = sys.argv[1:] if len(sys.argv) > 1 else None
    
    # Run tests
    exit_code = run_tests(test_args)
    
    # Print results
    print("=" * 60)
    if exit_code == 0:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
