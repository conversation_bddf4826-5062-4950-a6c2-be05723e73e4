# Deployment Guide - PR Statement Generator Backend

This guide covers Docker deployment options for the PR Statement Generator Backend, optimized for AWS ECS and other container orchestration platforms.

## Quick Start

### Local Development with Docker

1. **Build the development image:**
   ```bash
   ./docker-build.sh
   ```

2. **Run with docker-compose:**
   ```bash
   # Set your API key
   export GROQ_API_KEY="your-groq-api-key"
   
   # Start the service
   docker-compose up
   ```

3. **Access the API:**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Health check: http://localhost:8000/health

### Production Deployment

1. **Build production image:**
   ```bash
   ./docker-build.sh --production --tag v1.0.0
   ```

2. **Run production container:**
   ```bash
   docker run -d \
     --name pr-generator-backend \
     -p 8000:8000 \
     -e GROQ_API_KEY="your-groq-api-key" \
     --restart unless-stopped \
     pr-generator-backend:v1.0.0
   ```

## Docker Images

### Development Image (`Dockerfile`)
- **Purpose:** Local development and testing
- **Features:**
  - Single-stage build
  - Includes development dependencies
  - Optimized for fast rebuilds
  - Single worker process

### Production Image (`Dockerfile.production`)
- **Purpose:** Production deployment
- **Features:**
  - Multi-stage build for smaller image size
  - Minimal runtime dependencies
  - Security hardened
  - Multiple worker processes
  - Optimized for performance

## AWS ECS Deployment

### Task Definition Example

```json
{
  "family": "pr-generator-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "pr-generator-backend",
      "image": "your-registry/pr-generator-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "LOG_LEVEL",
          "value": "INFO"
        }
      ],
      "secrets": [
        {
          "name": "GROQ_API_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:groq-api-key"
        }
      ],
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      },
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/pr-generator-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Service Configuration

```json
{
  "serviceName": "pr-generator-backend",
  "cluster": "your-cluster",
  "taskDefinition": "pr-generator-backend:1",
  "desiredCount": 2,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": ["subnet-12345", "subnet-67890"],
      "securityGroups": ["sg-abcdef"],
      "assignPublicIp": "ENABLED"
    }
  },
  "loadBalancers": [
    {
      "targetGroupArn": "arn:aws:elasticloadbalancing:region:account:targetgroup/pr-generator/****************",
      "containerName": "pr-generator-backend",
      "containerPort": 8000
    }
  ]
}
```

## Environment Variables

### Required
- `GROQ_API_KEY`: Your Groq API key for LLM services

### Optional
- `LOG_LEVEL`: Logging level (default: INFO)
- `MODEL_NAME`: LLM model name (default: meta-llama/llama-4-scout-17b-16e-instruct)

## Security Considerations

1. **Non-root user:** Containers run as non-root user (uid: 1000)
2. **Minimal base image:** Uses Python slim image
3. **No cache:** Pip cache disabled to reduce image size
4. **Secret management:** Use AWS Secrets Manager for API keys
5. **Network security:** Configure security groups appropriately

## Monitoring and Health Checks

### Health Check Endpoint
- **URL:** `/health`
- **Method:** GET
- **Expected Response:** 200 OK with JSON status

### Container Health Check
- **Interval:** 30 seconds
- **Timeout:** 5 seconds
- **Retries:** 3
- **Start Period:** 60 seconds

### Logging
- **Format:** JSON structured logs
- **Level:** Configurable via LOG_LEVEL
- **Destination:** stdout (captured by container runtime)

## Scaling Considerations

### Horizontal Scaling
- **Stateless:** Application is stateless and can be scaled horizontally
- **Load Balancer:** Use ALB/NLB for traffic distribution
- **Auto Scaling:** Configure ECS auto scaling based on CPU/memory metrics

### Resource Requirements
- **Minimum:** 256 CPU units, 512 MB memory
- **Recommended:** 512 CPU units, 1024 MB memory
- **High Load:** 1024 CPU units, 2048 MB memory

## Troubleshooting

### Common Issues

1. **Container fails to start:**
   - Check GROQ_API_KEY is set
   - Verify network connectivity
   - Check logs for initialization errors

2. **Health check failures:**
   - Verify port 8000 is accessible
   - Check application logs
   - Ensure sufficient startup time

3. **Performance issues:**
   - Monitor CPU/memory usage
   - Check worker process count
   - Review API response times

### Debug Commands

```bash
# Check container logs
docker logs pr-generator-backend

# Execute shell in running container
docker exec -it pr-generator-backend /bin/bash

# Test health endpoint
curl http://localhost:8000/health

# Check container resource usage
docker stats pr-generator-backend
```
