[tool:pytest]
# Pytest configuration for PR Statement Generator Backend

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API endpoint tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (if pytest-cov is installed)
# addopts = --cov=. --cov-report=html --cov-report=term-missing

# Asyncio configuration
asyncio_mode = auto
